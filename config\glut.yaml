default:
  logger:
    log_level: "INFO"
  service:
    host: "127.0.0.1"
    port: 8282
    cert_file: "ssl_certs/localhost.crt"
    cert_key: "ssl_certs/localhost.key"
  chat_engine:
    model_root: "models"
    handler_search_path:
      - "src/handlers"
    handler_configs:
      LamClient:
        module: client/h5_rendering_client/client_handler_lam
        asset_path: "lam_samples/barbara.zip"
        concurrent_limit: 5
      SileroVad:
        module: vad/silerovad/vad_handler_silero
        speaking_threshold: 0.5
        start_delay: 2048
        end_delay: 5000
        buffer_look_back: 5000
        speech_padding: 512
      SenseVoice:
        enabled: True
        module: asr/sensevoice/asr_handler_sensevoice
        model_name: "iic/SenseVoiceSmall"
      Edge_TTS:
        enabled: True
        module: tts/edgetts/tts_handler_edgetts
        voice: "zh-CN-YunyangNeural"
      LLM_Bailian:
        enabled: True
        module: llm/openai_compatible/llm_handler_openai_compatible
        model_name: "glm-4-flash-250414"
        enable_video_input: False # ensure your llm support video input
        # model_name: "gemini-2.0-flash"
        system_prompt: "请你扮演一个 AI 助手，用简短的两三句对话来回答用户的问题，并在对话内容中加入合适的标点符号，不需要讨论标点符号相关的内容"
        api_url: "https://open.bigmodel.cn/api/paas/v4"
        # api_url: 'http://127.0.0.1:11434/v1' # ollama
        # api_url: 'https://generativelanguage.googleapis.com/v1beta/openai/'
        api_key: "82e834019b194305940ef41b2f558cf9.Ps83ScnCqULMESoh" # default=os.getenv("DASHSCOPE_API_KEY")
      LAM_Driver:
        module: avatar/lam/avatar_handler_lam_audio2expression
