{"_name_or_path": "openbmb/MiniCPM-o-2_6", "architectures": ["MiniCPMO"], "attention_dropout": 0.0, "bos_token_id": 151643, "eos_token_id": 151645, "hidden_act": "silu", "hidden_size": 3584, "initializer_range": 0.02, "intermediate_size": 18944, "max_position_embeddings": 32768, "max_window_layers": 28, "num_attention_heads": 28, "num_hidden_layers": 28, "num_key_value_heads": 4, "rms_norm_eps": 1e-06, "rope_theta": 1000000.0, "sliding_window": 131072, "tie_word_embeddings": false, "use_sliding_window": false, "vocab_size": 151700, "batch_vision_input": true, "drop_vision_last_layer": false, "image_size": 448, "audio_chunk_length": 1.0, "audio_config": {"_name_or_path": "openai/whisper-medium", "architectures": ["MiniCPMWhisperEncoder"], "begin_suppress_tokens": [220, 50257], "bos_token_id": 50257, "d_model": 1024, "decoder_attention_heads": 16, "decoder_ffn_dim": 4096, "decoder_layers": 24, "decoder_start_token_id": 50258, "encoder_attention_heads": 16, "encoder_ffn_dim": 4096, "encoder_layers": 24, "eos_token_id": 50257, "forced_decoder_ids": [[1, 50259], [2, 50359], [3, 50363]], "max_length": 448, "model_type": "whisper", "num_hidden_layers": 24, "pad_token_id": 50257, "suppress_tokens": [1, 2, 7, 8, 9, 10, 14, 25, 26, 27, 28, 29, 31, 58, 59, 60, 61, 62, 63, 90, 91, 92, 93, 359, 503, 522, 542, 873, 893, 902, 918, 922, 931, 1350, 1853, 1982, 2460, 2627, 3246, 3253, 3268, 3536, 3846, 3961, 4183, 4667, 6585, 6647, 7273, 9061, 9383, 10428, 10929, 11938, 12033, 12331, 12562, 13793, 14157, 14635, 15265, 15618, 16553, 16604, 18362, 18956, 20075, 21675, 22520, 26130, 26161, 26435, 28279, 29464, 31650, 32302, 32470, 36865, 42863, 47425, 49870, 50254, 50258, 50358, 50359, 50360, 50361, 50362], "torch_dtype": "float32"}, "audio_pool_step": 2, "auto_map": {"AutoConfig": "configuration_minicpm.MiniCPMOConfig", "AutoModel": "modeling_minicpmo.MiniCPMO", "AutoModelForCausalLM": "modeling_minicpmo.MiniCPMO"}, "chunk_input": true, "listen_speak_type": "asr", "model_type": "minicpmo", "patch_size": 14, "query_num": 64, "slice_config": {"max_slice_nums": 9, "model_type": "minicpmv"}, "slice_mode": true, "torch_dtype": "bfloat16", "transformers_version": "4.44.2", "tts_config": {"model_type": "conditional_chattts", "llm_dim": 3584}, "use_cache": true, "use_image_id": true, "version": 2.6, "vision_batch_size": 16, "vision_config": {"hidden_size": 1152, "image_size": 980, "intermediate_size": 4304, "model_type": "siglip_vision_model", "num_attention_heads": 16, "num_hidden_layers": 27, "patch_size": 14}}