{"add_bos_token": false, "add_prefix_space": false, "added_tokens_decoder": {"128244": {"content": "<unk>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151643": {"content": "<|endoftext|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151644": {"content": "<|im_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151645": {"content": "<|im_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151646": {"content": "<|object_ref_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151647": {"content": "<|object_ref_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151648": {"content": "<|box_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151649": {"content": "<|box_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151650": {"content": "<|quad_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151651": {"content": "<|quad_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151652": {"content": "<|vision_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151653": {"content": "<|vision_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151654": {"content": "<|vision_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151655": {"content": "<|image_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151656": {"content": "<|video_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151657": {"content": "<tool_call>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151658": {"content": "</tool_call>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151659": {"content": "<|fim_prefix|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151660": {"content": "<|fim_middle|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151661": {"content": "<|fim_suffix|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151662": {"content": "<|fim_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151663": {"content": "<|repo_name|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151664": {"content": "<|file_sep|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151665": {"content": "<image>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151666": {"content": "</image>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151667": {"content": "<ref>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151668": {"content": "</ref>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151669": {"content": "<box>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151670": {"content": "</box>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151671": {"content": "<quad>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151672": {"content": "</quad>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151673": {"content": "<point>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151674": {"content": "</point>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151675": {"content": "<slice>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151676": {"content": "</slice>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151677": {"content": "<image_id>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151678": {"content": "</image_id>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151679": {"content": "<unit>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151680": {"content": "</unit>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151681": {"content": "<asr>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151682": {"content": "</asr>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151683": {"content": "<query>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151684": {"content": "</query>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151685": {"content": "<|audio_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151686": {"content": "<|audio|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151687": {"content": "<|audio_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151688": {"content": "<|spk_bos|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151689": {"content": "<|spk|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151690": {"content": "<|spk_eos|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151691": {"content": "<|tts_bos|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151692": {"content": "<|tts_eos|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151693": {"content": "<|listen|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151694": {"content": "<|speak|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151695": {"content": "<|interrupt|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151696": {"content": "<|vad_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151697": {"content": "<|vad_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151698": {"content": "<reserved_43>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151699": {"content": "<reserved_53>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "additional_special_tokens": ["<image>", "</image>", "<ref>", "</ref>", "<box>", "</box>", "<quad>", "</quad>", "<point>", "</point>", "<slice>", "</slice>", "<image_id>", "</image_id>", "<unit>", "</unit>", "<asr>", "</asr>", "<query>", "</query>", "<|audio_start|>", "<|audio|>", "<|audio_end|>", "<|spk_bos|>", "<|spk|>", "<|spk_eos|>", "<|tts_bos|>", "<|tts_eos|>", "<|listen|>", "<|speak|>", "<|interrupt|>", "<|vad_start|>", "<|vad_end|>", "<reserved_43>", "<reserved_53>"], "bos_token": "<|im_start|>", "chat_template": "{%- if tools %}\n    {{- '<|im_start|>system\\n' }}\n    {%- if messages[0]['role'] == 'system' %}\n        {{- messages[0]['content'] }}\n    {%- else %}\n        {{- 'You are Qwen, created by Alibaba Cloud. You are a helpful assistant.' }}\n    {%- endif %}\n    {{- \"\\n\\n# Tools\\n\\nYou may call one or more functions to assist with the user query.\\n\\nYou are provided with function signatures within <tools></tools> XML tags:\\n<tools>\" }}\n    {%- for tool in tools %}\n        {{- \"\\n\" }}\n        {{- tool | tojson }}\n    {%- endfor %}\n    {{- \"\\n</tools>\\n\\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\\n<tool_call>\\n{\\\"name\\\": <function-name>, \\\"arguments\\\": <args-json-object>}\\n</tool_call><|im_end|>\\n\" }}\n{%- else %}\n    {%- if messages[0]['role'] == 'system' %}\n        {{- '<|im_start|>system\\n' + messages[0]['content'] + '<|im_end|>\\n' }}\n    {%- else %}\n        {{- '<|im_start|>system\\nYou are Qwen, created by <PERSON>baba Cloud. You are a helpful assistant.<|im_end|>\\n' }}\n    {%- endif %}\n{%- endif %}\n{%- for message in messages %}\n    {%- if (message.role == \"user\") or (message.role == \"system\" and not loop.first) or (message.role == \"assistant\" and not message.tool_calls) %}\n        {{- '<|im_start|>' + message.role + '\\n' + message.content + '<|im_end|>' + '\\n' }}\n    {%- elif message.role == \"assistant\" %}\n        {{- '<|im_start|>' + message.role }}\n        {%- if message.content %}\n            {{- '\\n' + message.content }}\n        {%- endif %}\n        {%- for tool_call in message.tool_calls %}\n            {%- if tool_call.function is defined %}\n                {%- set tool_call = tool_call.function %}\n            {%- endif %}\n            {{- '\\n<tool_call>\\n{\"name\": \"' }}\n            {{- tool_call.name }}\n            {{- '\", \"arguments\": ' }}\n            {{- tool_call.arguments | tojson }}\n            {{- '}\\n</tool_call>' }}\n        {%- endfor %}\n        {{- '<|im_end|>\\n' }}\n    {%- elif message.role == \"tool\" %}\n        {%- if (loop.index0 == 0) or (messages[loop.index0 - 1].role != \"tool\") %}\n            {{- '<|im_start|>user' }}\n        {%- endif %}\n        {{- '\\n<tool_response>\\n' }}\n        {{- message.content }}\n        {{- '\\n</tool_response>' }}\n        {%- if loop.last or (messages[loop.index0 + 1].role != \"tool\") %}\n            {{- '<|im_end|>\\n' }}\n        {%- endif %}\n    {%- endif %}\n{%- endfor %}\n{%- if add_generation_prompt %}\n    {{- '<|im_start|>assistant\\n' }}\n{%- endif %}\n", "clean_up_tokenization_spaces": false, "eos_token": "<|im_end|>", "errors": "replace", "model_max_length": 131072, "pad_token": "<|endoftext|>", "split_special_tokens": false, "auto_map": {"AutoTokenizer": ["tokenization_minicpmo_fast.MiniCPMOTokenizerFast", null]}, "tokenizer_class": "MiniCPMOTokenizerFast", "unk_token": "<unk>"}