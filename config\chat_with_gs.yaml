default:
  logger:
    log_level: "DEBUG"
  service:
    host: "0.0.0.0"
    port: 8282
    cert_file: "ssl_certs/localhost.crt"
    cert_key: "ssl_certs/localhost.key"
  chat_engine:
    model_root: "models"
    handler_search_path:
      - "src/handlers"
    turn_config:
        turn_provider: "turn_server"
        urls: 
          - "turn:***********:3478"
          - "turns:***********:5349"
        username: "username"
        credential: "password"
    handler_configs:
      #LamClient:
        #module: client/h5_rendering_client/client_handler_lam
        #asset_path: "/ai/zz/OpenAvatarChat-main/src/handlers/client/h5_rendering_client/lam_samples/james.zip"
      RtcClient:  # 改为RTC客户端  
        module: client/rtc_client/client_handler_rtc  
        concurrent_limit: 5
      SileroVad:
        module: vad/silerovad/vad_handler_silero
        speaking_threshold: 0.50
        start_delay: 2048
        end_delay: 10000
        buffer_look_back: 5000
        speech_padding: 512
      SenseVoice:
        enabled: True
        module: asr/sensevoice/asr_handler_sensevoice
        # model_name: "/ai/zz/OpenAvatarChat-main/models/iic/voxtral-mini"
        model_name: "/ai/zz/OpenAvatarChat-main/models/iic/SenseVoiceSmall"
      # MiniCPM-o:
      #   enabled: True
      #   module: llm/minicpm/llm_handler_minicpm
      #   model_name: "MiniCPM-o-2_6"
      #   # model_name: "MiniCPM-o-2_6-int4"
      #   voice_prompt: "你是一个AI助手。你能接受视频，音频和文本输入并输出语音和文本。模仿输入音频中的声音特征。"
      #   assistant_prompt: "作为助手，你将使用这种声音风格说话。"
      #   enable_video_input: True
      #   skip_video_frame: 2 
        # model_name: iic/SenseVoiceSmall
      # CosyVoice:
      #   enabled: True
      #   module: tts/bailian_tts/tts_handler_cosyvoice_bailian
      #   voice: "longxiaocheng"
      #   model_name: "cosyvoice-v1"
      #   api_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      #   api_key: "sk-50a8ddfd771d497fa223f5526224ce3b"
      # 本地
      # CosyVoice:  
      #   enabled: True  
      #   module: tts/cosyvoice/tts_handler_cosyvoice  
      #   model_name: "/ai/zz/OpenAvatarChat-main/models/iic/CosyVoice-300M-SFT"  
      #   spk_id: "中文男"  
      #   sample_rate: 24000  
      #   process_num: 2

#另外一个声音设置方案
      Edge_TTS:
        enabled: True 
        module: tts/edgetts/tts_handler_edgetts
        # voice: "zh-CN-XiaoxiaoNeural"
        voice: "zh-CN-YunxiNeural"
        rate: "+25%"

      LLM_Bailian:
        enabled: True
        module: llm/openai_compatible/llm_handler_openai_compatible
        # model_name: "qwen2.5-vl-7b-instruct"  # 修改为与API返回一致的模型名称
        model_name: "Qwen2.5-VL-7B"
        enable_video_input: True # ensure your llm support video input
        system_prompt: "你是杰创智能的 AI 助手，你非常熟悉杰创智能的背景及业务，并服务于客户和其他对象，你需要用简短的两三句对话来回答用户的问题，并在对话内容中加入合适的标点符号，不需要讨论标点符号相关的内容"
        #api_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
        # api_url: "http://10.10.20.42:8901/v1/chat/completions"  # 添加/v1路径
        # api_key: "Bearer jc888"
        api_url: "http://10.10.20.42:8901/v1"  # 添加/v1路径
        api_key: "jc888"


        # model_name: "qwen-vl-max"
        # enable_video_input: True
        # system_prompt: "请你扮演一个 AI 助手，用简短的两三句对话来回答用户的问题，并在对话内容中加入合适的标点符号，不需要讨论标点符号相关的内容"
        # api_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
        # api_key: "sk-50a8ddfd771d497fa223f5526224ce3b"



        
      # LiteAvatar:
      #   module: avatar/liteavatar/avatar_handler_liteavatar
      #   #avatar_name: 20250408/sample_data
      #   avatar_name: 20250612/P17L8MoMPLjyWLWn7-jCrnAw
      #   fps: 25
      #   debug: false
      #   enable_fast_mode: false
      #   use_gpu: true
      AvatarMusetalk:
        module: avatar/musetalk/avatar_handler_musetalk
        fps: 15  # Video frame rate
        batch_size: 4  # Batch processing frame count
        avatar_video_path: "/ai/zz/OpenAvatarChat-main/src/handlers/avatar/musetalk/MuseTalk/data/video/数字人完整-APNG.png"  # Initialization video path
        # avatar_video_path: "/ai/zz/OpenAvatarChat-main/src/handlers/avatar/musetalk/MuseTalk/data/video/509_raw.MP4"
        avatar_model_dir: "models/musetalk/avatar_model"  # Default avatar model directory
        force_create_avatar: false  # Whether to force regenerate digital human data
        # debug: True  # Whether to enable debug mode
        # debug_save_handler_audio: true  # Whether to save audio frames
        # debug_replay_speech_id: "speech-d9o18xrugl6-1"  # Speech ID for replay


