{"metadata": {"total_size": 17349994056}, "weight_map": {"apm.conv1.bias": "model-00004-of-00004.safetensors", "apm.conv1.weight": "model-00004-of-00004.safetensors", "apm.conv2.bias": "model-00004-of-00004.safetensors", "apm.conv2.weight": "model-00004-of-00004.safetensors", "apm.embed_positions.weight": "model-00004-of-00004.safetensors", "apm.layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.0.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.0.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.0.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.0.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.0.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.0.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.0.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.0.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.0.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.0.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.0.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.0.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.0.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.0.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.0.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.1.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.1.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.1.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.1.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.1.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.1.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.1.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.1.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.1.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.1.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.1.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.1.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.1.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.1.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.1.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.10.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.10.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.10.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.10.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.10.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.10.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.10.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.10.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.10.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.10.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.10.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.10.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.10.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.10.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.10.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.11.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.11.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.11.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.11.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.11.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.11.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.11.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.11.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.11.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.11.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.11.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.11.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.11.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.11.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.11.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.12.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.12.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.12.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.12.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.12.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.12.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.12.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.12.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.12.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.12.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.12.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.12.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.12.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.12.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.12.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.13.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.13.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.13.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.13.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.13.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.13.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.13.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.13.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.13.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.13.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.13.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.13.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.13.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.13.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.13.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.14.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.14.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.14.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.14.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.14.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.14.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.14.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.14.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.14.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.14.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.14.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.14.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.14.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.14.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.14.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.15.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.15.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.15.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.15.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.15.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.15.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.15.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.15.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.15.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.15.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.15.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.15.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.15.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.15.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.15.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.16.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.16.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.16.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.16.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.16.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.16.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.16.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.16.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.16.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.16.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.16.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.16.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.16.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.16.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.16.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.17.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.17.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.17.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.17.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.17.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.17.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.17.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.17.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.17.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.17.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.17.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.17.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.17.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.17.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.17.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.18.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.18.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.18.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.18.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.18.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.18.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.18.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.18.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.18.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.18.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.18.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.18.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.18.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.18.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.18.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.19.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.19.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.19.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.19.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.19.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.19.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.19.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.19.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.19.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.19.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.19.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.19.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.19.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.19.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.19.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.2.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.2.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.2.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.2.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.2.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.2.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.2.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.2.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.2.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.2.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.2.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.2.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.2.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.2.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.2.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.20.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.20.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.20.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.20.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.20.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.20.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.20.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.20.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.20.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.20.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.20.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.20.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.20.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.20.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.20.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.21.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.21.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.21.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.21.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.21.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.21.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.21.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.21.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.21.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.21.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.21.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.21.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.21.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.21.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.21.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.22.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.22.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.22.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.22.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.22.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.22.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.22.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.22.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.22.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.22.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.22.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.22.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.22.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.22.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.22.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.23.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.23.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.23.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.23.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.23.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.23.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.23.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.23.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.23.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.23.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.23.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.23.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.23.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.23.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.23.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.3.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.3.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.3.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.3.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.3.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.3.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.3.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.3.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.3.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.3.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.3.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.3.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.3.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.3.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.3.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.4.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.4.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.4.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.4.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.4.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.4.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.4.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.4.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.4.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.4.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.4.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.4.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.4.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.4.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.4.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.5.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.5.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.5.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.5.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.5.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.5.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.5.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.5.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.5.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.5.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.5.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.5.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.5.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.5.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.5.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.6.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.6.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.6.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.6.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.6.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.6.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.6.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.6.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.6.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.6.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.6.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.6.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.6.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.6.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.6.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.7.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.7.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.7.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.7.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.7.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.7.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.7.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.7.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.7.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.7.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.7.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.7.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.7.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.7.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.7.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.8.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.8.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.8.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.8.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.8.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.8.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.8.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.8.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.8.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.8.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.8.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.8.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.8.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.8.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.8.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.9.fc1.bias": "model-00004-of-00004.safetensors", "apm.layers.9.fc1.weight": "model-00004-of-00004.safetensors", "apm.layers.9.fc2.bias": "model-00004-of-00004.safetensors", "apm.layers.9.fc2.weight": "model-00004-of-00004.safetensors", "apm.layers.9.final_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.9.final_layer_norm.weight": "model-00004-of-00004.safetensors", "apm.layers.9.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.9.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.9.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.9.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.9.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.9.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "apm.layers.9.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "apm.layers.9.self_attn_layer_norm.bias": "model-00004-of-00004.safetensors", "apm.layers.9.self_attn_layer_norm.weight": "model-00004-of-00004.safetensors", "audio_projection_layer.linear1.bias": "model-00004-of-00004.safetensors", "audio_projection_layer.linear1.weight": "model-00004-of-00004.safetensors", "audio_projection_layer.linear2.bias": "model-00004-of-00004.safetensors", "audio_projection_layer.linear2.weight": "model-00004-of-00004.safetensors", "llm.lm_head.weight": "model-00004-of-00004.safetensors", "llm.model.embed_tokens.weight": "model-00001-of-00004.safetensors", "llm.model.layers.0.input_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.0.mlp.down_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.0.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.0.mlp.up_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.0.post_attention_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.0.self_attn.k_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.0.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.0.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.0.self_attn.q_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.0.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.0.self_attn.v_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.0.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.1.input_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.1.mlp.down_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.1.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.1.mlp.up_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.1.post_attention_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.1.self_attn.k_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.1.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.1.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.1.self_attn.q_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.1.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.1.self_attn.v_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.1.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.10.input_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.10.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.10.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.10.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.10.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.10.self_attn.k_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.10.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.10.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.10.self_attn.q_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.10.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.10.self_attn.v_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.10.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.11.input_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.11.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.11.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.11.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.11.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.11.self_attn.k_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.11.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.11.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.11.self_attn.q_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.11.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.11.self_attn.v_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.11.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.12.input_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.12.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.12.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.12.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.12.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.12.self_attn.k_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.12.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.12.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.12.self_attn.q_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.12.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.12.self_attn.v_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.12.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.13.input_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.13.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.13.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.13.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.13.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.13.self_attn.k_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.13.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.13.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.13.self_attn.q_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.13.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.13.self_attn.v_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.13.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.14.input_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.14.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.14.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.14.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.14.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.14.self_attn.k_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.14.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.14.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.14.self_attn.q_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.14.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.14.self_attn.v_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.14.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.15.input_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.15.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.15.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.15.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.15.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.15.self_attn.k_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.15.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.15.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.15.self_attn.q_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.15.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.15.self_attn.v_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.15.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.16.input_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.16.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.16.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.16.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.16.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.16.self_attn.k_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.16.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.16.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.16.self_attn.q_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.16.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.16.self_attn.v_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.16.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.17.input_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.17.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.17.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.17.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.17.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.17.self_attn.k_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.17.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.17.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.17.self_attn.q_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.17.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.17.self_attn.v_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.17.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.18.input_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.18.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.18.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.18.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.18.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.18.self_attn.k_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.18.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.18.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.18.self_attn.q_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.18.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.18.self_attn.v_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.18.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.19.input_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.19.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.19.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.19.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.19.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.19.self_attn.k_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.19.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.19.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.19.self_attn.q_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.19.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.19.self_attn.v_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.19.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.2.input_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.2.mlp.down_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.2.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.2.mlp.up_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.2.post_attention_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.2.self_attn.k_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.2.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.2.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.2.self_attn.q_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.2.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.2.self_attn.v_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.2.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.20.input_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.20.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.20.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.20.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.20.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.20.self_attn.k_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.20.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.20.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.20.self_attn.q_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.20.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.20.self_attn.v_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.20.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.21.input_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.21.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.21.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.21.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.21.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.21.self_attn.k_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.21.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.21.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.21.self_attn.q_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.21.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.21.self_attn.v_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.21.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.22.input_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.22.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.22.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.22.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.22.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.22.self_attn.k_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.22.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.22.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.22.self_attn.q_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.22.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.22.self_attn.v_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.22.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.23.input_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.23.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.23.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.23.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.23.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.23.self_attn.k_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.23.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.23.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.23.self_attn.q_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.23.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.23.self_attn.v_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.23.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.24.input_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.24.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.24.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.24.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.24.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.24.self_attn.k_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.24.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.24.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.24.self_attn.q_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.24.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.24.self_attn.v_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.24.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.25.input_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.25.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.25.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.25.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.25.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.25.self_attn.k_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.25.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.25.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.25.self_attn.q_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.25.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.25.self_attn.v_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.25.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.26.input_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.26.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.26.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.26.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.26.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.26.self_attn.k_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.26.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.26.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.26.self_attn.q_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.26.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.26.self_attn.v_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.26.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.27.input_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.27.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.27.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.27.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.27.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "llm.model.layers.27.self_attn.k_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.27.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.27.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.27.self_attn.q_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.27.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.27.self_attn.v_proj.bias": "model-00003-of-00004.safetensors", "llm.model.layers.27.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "llm.model.layers.3.input_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.3.mlp.down_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.3.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.3.mlp.up_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.3.post_attention_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.3.self_attn.k_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.3.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.3.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.3.self_attn.q_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.3.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.3.self_attn.v_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.3.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.4.input_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.4.mlp.down_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.4.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.4.mlp.up_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.4.post_attention_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.4.self_attn.k_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.4.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.4.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.4.self_attn.q_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.4.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.4.self_attn.v_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.4.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.5.input_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.5.mlp.down_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.5.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.5.mlp.up_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.5.post_attention_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.5.self_attn.k_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.5.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.5.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.5.self_attn.q_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.5.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.5.self_attn.v_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.5.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.6.input_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.6.mlp.down_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.6.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.6.mlp.up_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.6.post_attention_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.6.self_attn.k_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.6.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.6.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.6.self_attn.q_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.6.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.6.self_attn.v_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.6.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.7.input_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.7.mlp.down_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.7.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.7.mlp.up_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.7.post_attention_layernorm.weight": "model-00001-of-00004.safetensors", "llm.model.layers.7.self_attn.k_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.7.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.7.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.7.self_attn.q_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.7.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.7.self_attn.v_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.7.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.8.input_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.8.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.8.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.8.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.8.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.8.self_attn.k_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.8.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.8.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.8.self_attn.q_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.8.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.8.self_attn.v_proj.bias": "model-00001-of-00004.safetensors", "llm.model.layers.8.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "llm.model.layers.9.input_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.9.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.9.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.9.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.9.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "llm.model.layers.9.self_attn.k_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.9.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.9.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.9.self_attn.q_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.9.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "llm.model.layers.9.self_attn.v_proj.bias": "model-00002-of-00004.safetensors", "llm.model.layers.9.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "llm.model.norm.weight": "model-00003-of-00004.safetensors", "resampler.attn.in_proj_bias": "model-00004-of-00004.safetensors", "resampler.attn.in_proj_weight": "model-00004-of-00004.safetensors", "resampler.attn.out_proj.bias": "model-00004-of-00004.safetensors", "resampler.attn.out_proj.weight": "model-00004-of-00004.safetensors", "resampler.kv_proj.weight": "model-00004-of-00004.safetensors", "resampler.ln_kv.bias": "model-00004-of-00004.safetensors", "resampler.ln_kv.weight": "model-00004-of-00004.safetensors", "resampler.ln_post.bias": "model-00004-of-00004.safetensors", "resampler.ln_post.weight": "model-00004-of-00004.safetensors", "resampler.ln_q.bias": "model-00004-of-00004.safetensors", "resampler.ln_q.weight": "model-00004-of-00004.safetensors", "resampler.proj": "model-00004-of-00004.safetensors", "resampler.query": "model-00004-of-00004.safetensors", "tts.dvae.coef": "model-00004-of-00004.safetensors", "tts.dvae.decoder.conv_in.0.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.conv_in.0.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.conv_in.2.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.conv_in.2.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.conv_out.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.0.coef": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.0.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.0.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.0.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.0.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.0.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.0.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.0.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.0.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.1.coef": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.1.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.1.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.1.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.1.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.1.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.1.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.1.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.1.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.10.coef": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.10.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.10.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.10.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.10.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.10.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.10.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.10.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.10.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.11.coef": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.11.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.11.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.11.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.11.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.11.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.11.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.11.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.11.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.2.coef": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.2.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.2.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.2.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.2.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.2.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.2.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.2.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.2.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.3.coef": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.3.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.3.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.3.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.3.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.3.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.3.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.3.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.3.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.4.coef": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.4.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.4.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.4.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.4.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.4.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.4.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.4.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.4.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.5.coef": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.5.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.5.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.5.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.5.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.5.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.5.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.5.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.5.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.6.coef": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.6.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.6.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.6.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.6.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.6.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.6.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.6.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.6.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.7.coef": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.7.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.7.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.7.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.7.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.7.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.7.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.7.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.7.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.8.coef": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.8.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.8.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.8.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.8.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.8.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.8.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.8.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.8.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.9.coef": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.9.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.9.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.9.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.9.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.9.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.9.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.9.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.decoder.decoder_block.9.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.downsample_conv.0.bias": "model-00004-of-00004.safetensors", "tts.dvae.downsample_conv.0.weight": "model-00004-of-00004.safetensors", "tts.dvae.downsample_conv.2.bias": "model-00004-of-00004.safetensors", "tts.dvae.downsample_conv.2.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.conv_in.0.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.conv_in.0.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.conv_in.2.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.conv_in.2.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.conv_out.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.0.coef": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.0.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.0.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.0.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.0.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.0.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.0.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.0.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.0.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.1.coef": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.1.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.1.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.1.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.1.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.1.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.1.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.1.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.1.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.10.coef": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.10.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.10.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.10.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.10.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.10.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.10.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.10.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.10.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.11.coef": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.11.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.11.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.11.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.11.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.11.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.11.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.11.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.11.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.2.coef": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.2.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.2.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.2.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.2.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.2.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.2.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.2.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.2.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.3.coef": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.3.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.3.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.3.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.3.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.3.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.3.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.3.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.3.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.4.coef": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.4.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.4.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.4.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.4.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.4.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.4.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.4.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.4.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.5.coef": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.5.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.5.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.5.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.5.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.5.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.5.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.5.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.5.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.6.coef": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.6.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.6.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.6.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.6.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.6.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.6.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.6.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.6.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.7.coef": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.7.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.7.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.7.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.7.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.7.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.7.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.7.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.7.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.8.coef": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.8.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.8.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.8.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.8.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.8.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.8.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.8.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.8.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.9.coef": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.9.dwconv.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.9.dwconv.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.9.norm.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.9.norm.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.9.pwconv1.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.9.pwconv1.weight": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.9.pwconv2.bias": "model-00004-of-00004.safetensors", "tts.dvae.encoder.decoder_block.9.pwconv2.weight": "model-00004-of-00004.safetensors", "tts.dvae.out_conv.weight": "model-00004-of-00004.safetensors", "tts.dvae.vq_layer.quantizer.rvqs.0.project_in.bias": "model-00004-of-00004.safetensors", "tts.dvae.vq_layer.quantizer.rvqs.0.project_in.weight": "model-00004-of-00004.safetensors", "tts.dvae.vq_layer.quantizer.rvqs.0.project_out.bias": "model-00004-of-00004.safetensors", "tts.dvae.vq_layer.quantizer.rvqs.0.project_out.weight": "model-00004-of-00004.safetensors", "tts.dvae.vq_layer.quantizer.rvqs.1.project_in.bias": "model-00004-of-00004.safetensors", "tts.dvae.vq_layer.quantizer.rvqs.1.project_in.weight": "model-00004-of-00004.safetensors", "tts.dvae.vq_layer.quantizer.rvqs.1.project_out.bias": "model-00004-of-00004.safetensors", "tts.dvae.vq_layer.quantizer.rvqs.1.project_out.weight": "model-00004-of-00004.safetensors", "tts.emb_code.0.weight": "model-00004-of-00004.safetensors", "tts.emb_code.1.weight": "model-00004-of-00004.safetensors", "tts.emb_code.2.weight": "model-00004-of-00004.safetensors", "tts.emb_code.3.weight": "model-00004-of-00004.safetensors", "tts.emb_text.weight": "model-00004-of-00004.safetensors", "tts.head_code.0.parametrizations.weight.original0": "model-00004-of-00004.safetensors", "tts.head_code.0.parametrizations.weight.original1": "model-00004-of-00004.safetensors", "tts.head_code.1.parametrizations.weight.original0": "model-00004-of-00004.safetensors", "tts.head_code.1.parametrizations.weight.original1": "model-00004-of-00004.safetensors", "tts.head_code.2.parametrizations.weight.original0": "model-00004-of-00004.safetensors", "tts.head_code.2.parametrizations.weight.original1": "model-00004-of-00004.safetensors", "tts.head_code.3.parametrizations.weight.original0": "model-00004-of-00004.safetensors", "tts.head_code.3.parametrizations.weight.original1": "model-00004-of-00004.safetensors", "tts.model.embed_tokens.weight": "model-00004-of-00004.safetensors", "tts.model.layers.0.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.0.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.0.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.0.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.0.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.0.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.0.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.0.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.0.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.1.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.1.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.1.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.1.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.1.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.1.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.1.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.1.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.1.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.10.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.10.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.10.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.10.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.10.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.10.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.10.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.10.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.10.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.11.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.11.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.11.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.11.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.11.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.11.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.11.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.11.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.11.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.12.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.12.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.12.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.12.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.12.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.12.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.12.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.12.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.12.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.13.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.13.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.13.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.13.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.13.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.13.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.13.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.13.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.13.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.14.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.14.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.14.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.14.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.14.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.14.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.14.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.14.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.14.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.15.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.15.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.15.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.15.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.15.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.15.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.15.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.15.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.15.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.16.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.16.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.16.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.16.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.16.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.16.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.16.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.16.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.16.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.17.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.17.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.17.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.17.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.17.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.17.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.17.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.17.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.17.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.18.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.18.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.18.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.18.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.18.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.18.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.18.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.18.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.18.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.19.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.19.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.19.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.19.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.19.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.19.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.19.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.19.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.19.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.2.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.2.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.2.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.2.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.2.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.2.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.2.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.2.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.2.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.3.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.3.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.3.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.3.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.3.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.3.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.3.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.3.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.3.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.4.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.4.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.4.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.4.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.4.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.4.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.4.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.4.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.4.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.5.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.5.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.5.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.5.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.5.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.5.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.5.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.5.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.5.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.6.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.6.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.6.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.6.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.6.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.6.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.6.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.6.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.6.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.7.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.7.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.7.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.7.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.7.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.7.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.7.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.7.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.7.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.8.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.8.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.8.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.8.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.8.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.8.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.8.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.8.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.8.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.9.input_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.9.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.9.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.9.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.9.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "tts.model.layers.9.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.9.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.9.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "tts.model.layers.9.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "tts.model.norm.weight": "model-00004-of-00004.safetensors", "tts.projector.linear1.bias": "model-00004-of-00004.safetensors", "tts.projector.linear1.weight": "model-00004-of-00004.safetensors", "tts.projector.linear2.bias": "model-00004-of-00004.safetensors", "tts.projector.linear2.weight": "model-00004-of-00004.safetensors", "vpm.embeddings.patch_embedding.bias": "model-00004-of-00004.safetensors", "vpm.embeddings.patch_embedding.weight": "model-00004-of-00004.safetensors", "vpm.embeddings.position_embedding.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.0.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.1.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.10.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.11.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.12.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.13.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.14.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.15.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.16.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.17.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.18.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.19.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.2.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.20.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.21.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.22.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.23.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.24.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.25.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.26.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.3.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.4.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.5.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.6.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.7.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.8.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.layer_norm1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.layer_norm1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.layer_norm2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.layer_norm2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.mlp.fc1.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.mlp.fc1.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.mlp.fc2.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.mlp.fc2.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.self_attn.k_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.self_attn.out_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.self_attn.out_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.self_attn.q_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.self_attn.v_proj.bias": "model-00004-of-00004.safetensors", "vpm.encoder.layers.9.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "vpm.post_layernorm.bias": "model-00004-of-00004.safetensors", "vpm.post_layernorm.weight": "model-00004-of-00004.safetensors"}}