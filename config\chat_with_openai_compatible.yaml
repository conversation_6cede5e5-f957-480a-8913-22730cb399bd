default:
  logger:
    log_level: "INFO"
  service:
    host: "0.0.0.0"
    port: 8282
    cert_file: "ssl_certs/localhost.crt"
    cert_key: "ssl_certs/localhost.key"
  chat_engine:
    model_root: "models"
    handler_search_path:
      - "src/handlers"
    turn_config:
        turn_provider: "turn_server"
        urls: ["turn:***********:3478", "turns:***********:5349"]
        username: "username"
        credential: "password"
    handler_configs:
      RtcClient:
        module: client/rtc_client/client_handler_rtc
      SileroVad:
        module: vad/silerovad/vad_handler_silero
        speaking_threshold: 0.5
        start_delay: 2048
        end_delay: 5000
        buffer_look_back: 5000
        speech_padding: 512
      SenseVoice:
        enabled: True
        module: asr/sensevoice/asr_handler_sensevoice
        model_name: "iic/SenseVoiceSmall"
      CosyVoice:
        enabled: True
        module: tts/cosyvoice/tts_handler_cosyvoice
        # api_url: 'http://127.0.0.1:50000/inference_sft' #run CosyVoice/runtime/python/fastapi/server.py
        model_name: "iic/CosyVoice-300M-SFT" # run cosyvoice in code
        spk_id: "中文女" # use sft model
        # ref_audio_path: "open-video-chat/src/third_party/CosyVoice/asset/zero_shot_prompt.wav" #use zero_shot model
        # ref_audio_text: "希望你以后能够做的比我还好呦。"
        sample_rate: 24000
        process_num: 2
      LLM_Bailian:
        enabled: True
        module: llm/openai_compatible/llm_handler_openai_compatible
        # model_name: "qwen2.5-vl-7b-instruct"  # 修改为与API返回一致的模型名称
        model_name: "Qwen2.5-VL-7B"
        enable_video_input: True # ensure your llm support video input
        system_prompt: "请你扮演一个 AI 助手，用简短的两三句对话来回答用户的问题，并在对话内容中加入合适的标点符号，不需要讨论标点符号相关的内容"
        #api_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
        # api_url: "http://10.10.20.42:8901/v1/chat/completions"  # 添加/v1路径
        # api_key: "Bearer jc888"
        api_url: "http://10.10.20.42:8901/v1"  # 添加/v1路径
        api_key: "jc888"
        
      LiteAvatar:
        module: avatar/liteavatar/avatar_handler_liteavatar
        avatar_name: 20250408/sample_data
        fps: 25
        debug: false
        enable_fast_mode: false
        use_gpu: true
